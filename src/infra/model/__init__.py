from functools import cache

from langchain.chat_models import init_chat_model
from langchain_core.language_models import BaseChatModel
from openai import OpenAI

from config.globalconfig import get_or_create_settings_ins

@cache
def get_chat_model(model_name: str = "default") -> BaseChatModel:
    config = get_or_create_settings_ins()
    models = config.models
    model_cfg = getattr(models, "default")
    if model_name in models.model_fields_set:
        model_cfg = getattr(models, model_name)
    llm = config.llms[model_cfg]
    return init_chat_model(
        llm.model,
        model_provider=llm.provider,
        **(llm.external_args or {}),
    )


@cache
def get_openai_client(model_name: str = "default", version: str = None) -> OpenAI:
    """
    根据模型名称和版本创建 OpenAI 客户端

    Args:
        model_name: 模型名称，对应配置文件中的模型配置
        version: 版本信息（可选，暂未使用）

    Returns:
        OpenAI: 配置好的 OpenAI 客户端实例
    """
    config = get_or_create_settings_ins()
    models = config.models

    # 获取模型配置名称
    model_cfg = getattr(models, "default")
    if model_name in models.model_fields_set:
        model_cfg = getattr(models, model_name)

    # 获取对应的 LLM 配置
    llm = config.llms[model_cfg]

    # 从 external_args 中提取 OpenAI 客户端所需的参数
    external_args = llm.external_args or {}

    # 创建 OpenAI 客户端
    client_kwargs = {}

    # 设置 base_url
    if "base_url" in external_args:
        client_kwargs["base_url"] = external_args["base_url"]

    # 设置 api_key
    if "api_key" in external_args:
        client_kwargs["api_key"] = external_args["api_key"]

    # 设置其他可能的参数
    if "timeout" in external_args:
        client_kwargs["timeout"] = external_args["timeout"]

    if "max_retries" in external_args:
        client_kwargs["max_retries"] = external_args["max_retries"]

    return OpenAI(**client_kwargs)


def create_chat_completion(model_name: str = "default", messages: list = None, thinking: str = "disabled", **kwargs):
    """
    创建聊天完成请求

    Args:
        model_name: 模型名称
        messages: 消息列表
        thinking: 思考模式，可选值: "disabled"(默认), "enabled", "auto"
        **kwargs: 其他参数

    Returns:
        聊天完成响应
    """
    config = get_or_create_settings_ins()
    models = config.models

    # 获取模型配置名称
    model_cfg = getattr(models, "default")
    if model_name in models.model_fields_set:
        model_cfg = getattr(models, model_name)

    # 获取对应的 LLM 配置
    llm = config.llms[model_cfg]

    # 获取 OpenAI 客户端
    client = get_openai_client(model_name)

    # 设置默认参数
    completion_kwargs = {
        "model": llm.model,
        "messages": messages or [],
        "temperature": llm.temperature,
        "max_tokens": llm.max_tokens,
    }

    # 处理 extra_body 参数
    extra_body = {}

    # 从配置文件中获取 extra_body
    if llm.extra_body:
        extra_body.update(llm.extra_body)

    # 设置思考参数，用户传入的参数优先于配置文件
    if thinking in ["disabled", "enabled", "auto"]:
        extra_body["thinking"] = {"type": thinking}

    # 如果有 extra_body 内容，添加到请求参数中
    if extra_body:
        completion_kwargs["extra_body"] = extra_body

    # 覆盖传入的其他参数
    completion_kwargs.update(kwargs)

    return client.chat.completions.create(**completion_kwargs)
